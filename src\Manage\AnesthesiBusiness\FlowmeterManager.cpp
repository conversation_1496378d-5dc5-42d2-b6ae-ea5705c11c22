#include <string.h>
#include <deque>
#include <algorithm>
#include <tuple>
#include <mutex>

#include "SystemSettingManager.h"
#include "String/UIStrings.h"
#include "AlarmManager.h"
#include "SettingSpinbox/SettingSpinbox.h"
#include "FlowmeterManager.h"
#include "Flowmeter.h"
#include "FlowmeterView.h"
#include "SystemConfigManager.h"
#include "UiConfig.h"
#include "RunModeManage.h"
#include "SystemTimeManager.h"
#include "DataManager.h"
#include "HistoryEventManager.h"
#include "FlowControlDialog.h"

#define OFF (0)
#define FLOW_CONTROL_DIGIT  (1)
#define FLOW_CONTROL_ZOOM   (0.1)
#define MAX_DIRECT_FLOW_VELOCITY (12.0)
#define MIN_O2_PCT_WITH_AIR (21)
#define MIN_O2_PCT_WITH_N2O (25)
#define AIR_OXYGEN_CONTENT  (21)
#define MIN_O2_VELOCITY_LPM (0.2)
#define MIN_BALANCE_FLOW_VELOCITY_LPM (0.1)
#define FLOAT_CAL_DEVIATION_FACTOR (0.000001)
#define PCT_100 (100)
#define MAX_VALUE (100000)
#define DOUBLE_EQUAL_DIF 1e-5

#define MIN_VELOCITY_PRECISION (0.1)
#define MIN_PCT_PRECISION      (0.1)

#define FLOW_INPUT_REGEX "^\\d+(\\.\\d)?"
#define PCT_INPUT_REGEX  "^\\d+"


enum class DATA_VALIDATOR_ERROR_CODE : short
{
    FLOW_CACL_NO_ERROR,
    TOTAL_VELOCITY_OUT_OF_HIGH_LIMIT,
    TOTAL_VELOCITY_OUT_OF_LOW_LIMIT,
    O2_VELOCITY_OUT_OF_HIGH_LIMIT,
    O2_VELOCITY_OUT_OF_LOW_LIMIT,
    BALANCE_GAS_OUT_OF_HIGH_LIMIT,
    BALANCE_GAS_OUT_OF_LOW_LIMIT,
    O2_PCT_TOO_LOW
};

//流量计颜色分类
enum FLOWMTER_COLOR
{
    FM_BLUE = 0,  //蓝色
    FM_GREY,      //灰色
    FM_WHITE_PLUS, //白格子色
    FM_WHITE,    //白色
    FM_GREEN,    //蓝绿色
    FM_YELLOW    //黄色
};

FLOWMETER_COLOR_DISCRIPTOR flowmter_color[]
{//与枚举值对颜色对应
    {BG_RGB(58,132,192), BG_RGB(58,132,197), 0},
    {BG_RGB(197,197,206),BG_RGB(197,197,206),0},
    {BG_RGB(255,255,255),BG_RGB(255,255,255),1},   //格子条
    {BG_RGB(255,255,255),BG_RGB(255,255,255),0},   //白色条
    {BG_RGB(76,125,70),  BG_RGB(76,125,70),  0},
    {BG_RGB(240,213,17), BG_RGB(240,213,17), 0}
};

QString ConvertControlTypeEnumToName(int type)
{
    if (type == CONTROL_TYPE::DIRECT_FLOW)
        return UIStrings::GetStr(STR_SINGLE_PIPE_FLOW_CONTROL);
    else if (type == CONTROL_TYPE::TOTAL_CONTROL)
        return UIStrings::GetStr(STR_TOTAL_FLOW_CONTROL);
    else
        return "";
}

QString ConvertBalanceTypeEnumToName(int type)
{
    switch (type) {
    case BALANCE_GAS_TYPE::NONE:
        return UIStrings::GetStr(STR_NONE);
        break;
    case BALANCE_GAS_TYPE::AIR_BALANCE:
        return UIStrings::GetStr(STR_GASNAME_AIR);
        break;
    case BALANCE_GAS_TYPE::N2O_BALANCE:
        return UIStrings::GetStr(STR_GASNAME_N2O);
        break;
    default:
        return "wrong";
    }
}

QString ConvertFlowTypeEnumToName(int type)
{
    switch (type)
    {
    case CONTROL_BUTTON_TYPE::AIR:
        return UIStrings::GetStr(STR_GASNAME_AIR);
        break;
    case CONTROL_BUTTON_TYPE::N2O:
        return UIStrings::GetStr(STR_GASNAME_N2O);
        break;
    case CONTROL_BUTTON_TYPE::O2PCT:
    case CONTROL_BUTTON_TYPE::O2:
        return UIStrings::GetStr(STR_GASNAME_O2);
        break;
    case CONTROL_BUTTON_TYPE::TOTAL:
        return UIStrings::GetStr(STR_TOTAL);
        break;
    default:
        return "";
        break;
    }
}


/* *
 * @brief Formula1 = flowPct / 100 * totalValue;  百分比轉流速;
 * @param flowPct
 * @param totalValue
 * @return 氣體流速
   */
inline static double Formula1(double totalValue, double flowPct)
{
    totalValue = QString::number(totalValue + FLOAT_CAL_DEVIATION_FACTOR, 'f', 3).toDouble();
    return flowPct / 100 * totalValue;
}

/* *
 * @brief Formula2 = (balancePct * 100 / (100 - AIR_OXYGEN_CONTENT)) / 100 * totalValue;  限定條件下的百分比轉流速  前置条件:(總流量控制模式 平衡氣體爲空氣)
 * @param balancePct 空气去除氧气后占总流量的百分比
 * @param totalValue
 * @return 原空气流速
   */
inline static double Formula2(double totalValue, double balancePct)
{
    totalValue = QString::number(totalValue + FLOAT_CAL_DEVIATION_FACTOR, 'f', 3).toDouble();
    return (balancePct * 100 / (100 - AIR_OXYGEN_CONTENT)) / 100 * totalValue;
}

/* *
 * @brief Formula3 流速转百分比
 * @param flowVelocity
 * @param totalValue
 * @return 百分比
   */
inline static double Formula3(const double totalValue, const double flowVelocity)
{
    return flowVelocity / totalValue * 100;
}


/* *
 * 前置条件：单管模式
 * @brief Formula4  返回在保持平衡气体值不变，但在总体中的占比变为100 - minO2Pct时的氧气流速
 * @param balanceValue 平衡气体流速
 * @param minO2Pct 氧气百分比
 * @return 氧气流速
   */
inline static double Formula4(const double balanceValue, const double minO2Pct)
{
    return balanceValue / ((100 - minO2Pct) / 100.0) - balanceValue;
}

/* *
 * 前置條件: balanceType == BALANCE_GAS_TYPE::AIR_BALANCE
 * @brief Formula5 從單管切換至總流量控制時
 * @param balanceValue
 * @param totalValue
 * @return 氧氣百分比
   */
inline static double Formula5(const double o2Value, const double balanceValue, const double totalValue)
{
    return (o2Value + (balanceValue  * AIR_OXYGEN_CONTENT / 100)) * 100 / totalValue;
}

/* *
 * @brief Formula6 保持百分比不变情况下 氧气流速为MIN_O2_VELOCITY_LPM的总流速
 * @param balanceValue
 * @return 总流速
   */
inline static double Formula6(const double balancePct)
{
    return MIN_O2_VELOCITY_LPM / (1 - balancePct * 100 / (100 - AIR_OXYGEN_CONTENT) / 100);
}


/* *
 * @brief Formula7  约束条件是空气流量达到最大值12L/min
 * @param balancePct
 * @return 流速
   */
inline static double Formula7(const double balancePct)
{
    return MAX_DIRECT_FLOW_VELOCITY * 100 / (balancePct * 100 / (100 - AIR_OXYGEN_CONTENT)) ;
}

bool isN2oO2RatioAbnormalInDirectControl(double o2Velocity, double balanceGasVelocity)
{
    double balanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);

    o2Velocity         = QString::number(o2Velocity + FLOAT_CAL_DEVIATION_FACTOR, 'f', 1).toDouble();
    balanceGasVelocity = QString::number(balanceGasVelocity + FLOAT_CAL_DEVIATION_FACTOR, 'f', 1).toDouble();

    double o2Pct = Formula3((o2Velocity + balanceGasVelocity), o2Velocity);
    int minO2Pct = balanceType == BALANCE_GAS_TYPE::AIR_BALANCE ? MIN_O2_PCT_WITH_AIR : MIN_O2_PCT_WITH_N2O;

    o2Pct = QString::number(o2Pct + FLOAT_CAL_DEVIATION_FACTOR, 'f', 1).toDouble();
    return o2Pct < minO2Pct;
};


FlowControlValueSt FlowmeterManager::ProcessBalanceTypeChangeInDirectControl(int oldBalanceType, int newBalanceType)
{
    std::ignore = oldBalanceType;
    double totalValue      = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE);
    double o2Velocity      = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);
    double balanceVelocity = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE);

    if (newBalanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
    {
        if (Formula3(totalValue, o2Velocity) < MIN_O2_PCT_WITH_N2O)
        {
            balanceVelocity = o2Velocity / (MIN_O2_PCT_WITH_N2O / 100.0) - o2Velocity;
        }
    }

    return {o2Velocity + balanceVelocity, o2Velocity, balanceVelocity};
}



/* *
 * 前置條件: ControlType==TOTAL_CONTROL
 * @brief Rule3 总流量控制 内部切换平衡气体时
 * @param oldBalanceType
 * @param newBalanceType
   */
FlowControlValueSt FlowmeterManager::ProcessBalanceTypeChangeInTotalControl(int oldBalanceType, int newBalanceType)
{
    double o2Pct = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);
    double totalValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE);

    int minO2Pct = newBalanceType == BALANCE_GAS_TYPE::AIR_BALANCE ? MIN_O2_PCT_WITH_AIR : MIN_O2_PCT_WITH_N2O;

    if(o2Pct < minO2Pct && o2Pct > 0)
    {
        o2Pct = minO2Pct;
    }
    double balancePct = 100 - o2Pct;

    if (totalValue == OFF)
        return {totalValue, o2Pct, balancePct};

    // BALANCE_GAS_TYPE::N2O_BALANCE -> BALANCE_GAS_TYPE::AIR_BALANCE
    if (oldBalanceType == BALANCE_GAS_TYPE::N2O_BALANCE && newBalanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
    {
        double tmpBalanceValue = Formula2(totalValue, balancePct);
        double tmpO2Value      = totalValue - tmpBalanceValue;
        double tmpTotalValue   = totalValue;
        if (tmpO2Value < MIN_O2_VELOCITY_LPM)
        {
            tmpTotalValue = Formula6(balancePct);
        }
        if (tmpBalanceValue > MAX_DIRECT_FLOW_VELOCITY)
        {
            tmpTotalValue = Formula7(balancePct);
        }
        totalValue = tmpTotalValue;
    }
    // BALANCE_GAS_TYPE::AIR_BALANCE -> BALANCE_GAS_TYPE::N2O_BALANCE
    else if (oldBalanceType == BALANCE_GAS_TYPE::AIR_BALANCE && newBalanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
    {
        double tmpBalanceVelocity = Formula1(totalValue, balancePct);
        double tmpO2Velocity      = totalValue - tmpBalanceVelocity;
        double tmpTotalValue      = totalValue;

        //氧气流速超过单管最大流速限制，通过减小总流量满足限制以保持氧浓度不变
        if(tmpO2Velocity > MAX_DIRECT_FLOW_VELOCITY)
        {
            tmpTotalValue = MAX_DIRECT_FLOW_VELOCITY * 100 / o2Pct;
        }

        if(tmpO2Velocity < MIN_O2_VELOCITY_LPM && tmpBalanceVelocity < MIN_BALANCE_FLOW_VELOCITY_LPM)
        {
            double tmpO2Velocity = MIN_O2_VELOCITY_LPM;
            tmpTotalValue = tmpO2Velocity * 100 / MIN_O2_PCT_WITH_N2O;
        }
        else if (tmpO2Velocity < MIN_O2_VELOCITY_LPM)
        {
            double tmpO2Velocity = MIN_O2_VELOCITY_LPM;
            tmpTotalValue = tmpO2Velocity * 100 / MIN_O2_PCT_WITH_N2O;
        }
        //笑气流速低于最小流速限制，通过增大总流速满足限制以保持氧浓度不变 增大的值为原笑气流速与最小流速之间的差值
        else if (tmpBalanceVelocity < MIN_BALANCE_FLOW_VELOCITY_LPM && tmpBalanceVelocity != OFF)
        {
            tmpTotalValue = MIN_BALANCE_FLOW_VELOCITY_LPM * 100 / balancePct;
        }

        totalValue = tmpTotalValue;
    }
    return {totalValue, o2Pct, balancePct};
}

bool FlowmeterManager::InitValue()
{
    int controlType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    int balanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);

    if(controlType == CONTROL_TYPE::TOTAL_CONTROL)
    {
        if (balanceType == BALANCE_GAS_TYPE::NONE)
        {
            SaveSettingData(OFF, PCT_100, OFF);
            mO2UserInputValue = PCT_100;
        }
        else if(balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
        {
            SaveSettingData(OFF, PCT_100, OFF);
            mO2UserInputValue = PCT_100;
        }
        else if(balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
        {
            SaveSettingData(OFF, PCT_100, OFF);
            mO2UserInputValue = PCT_100;
        }
    }
    else
    {
        SaveSettingData(OFF, OFF, OFF);
        mO2UserInputValue = OFF;
    }
    return true;
}

FlowmeterManager::FlowmeterManager()
{
    mTotalValue = 0;
    HistoryEventManager::GetInstance()->RegisterOperateLogConvertFunc(LOG_FLOWMETER_CHANGE, [](const HistoryEventSt &paramSt)->QString {
        int operateId = paramSt.mParameter1;
        switch (operateId)
        {
        case CONTROL_MODE_CHANGE:
        {
            int oldType = paramSt.mParameter2;
            int newType = paramSt.mParameter3;

            return UIStrings::GetStr(STR_CHANGE_CONTROL_MODE)
                    + ": "
                    + ConvertControlTypeEnumToName(oldType)
                    + "->"
                    + ConvertControlTypeEnumToName(newType);
        }
            break;
        case BALANCE_MODE_CHANGE:
        {
            int oldType = paramSt.mParameter2;
            int newType = paramSt.mParameter3;

            return UIStrings::GetStr(STR_CHANGE_BALANCE_MODE)
                    +": "
                    + ConvertBalanceTypeEnumToName(oldType)
                    + "->"
                    + ConvertBalanceTypeEnumToName(newType);
        }
            break;
        case VALUE_CHANGE:
        {
            int gasType = paramSt.mParameter2;
            int oldValue = paramSt.mParameter3;
            int value = paramSt.mParameter4;
            int controlType = paramSt.mParameter5;

            int digit{};
            ALL_STRINGS_ENUM unitStrId{};
            if (gasType == CONTROL_BUTTON_TYPE::TOTAL)
            {
                digit = 1;
                unitStrId = STR_UNIT_LPERMIN;
            }
            else
            {
                digit = controlType == CONTROL_TYPE::DIRECT_FLOW ? 1 : 0;
                unitStrId = controlType == CONTROL_TYPE::DIRECT_FLOW ? ALL_STRINGS_ENUM::STR_UNIT_LPERMIN : STR_UNIT_PERCENTAGE;
            }

            return UIStrings::GetStr(STR_CHANGE_FLOW_VALUE)
                    +": "
                    + ConvertFlowTypeEnumToName(gasType)
                    + " "
                    + QString::number(((double)oldValue / qPow(10, digit)), 'f', digit)
                    + UIStrings::GetStr(unitStrId)
                    + "->"
                    + QString::number(((double)value / qPow(10, digit)), 'f', digit)
                    + UIStrings::GetStr(unitStrId);
        }
            break;
        case Quick_Setting:
        {
            int totalValue = paramSt.mParameter2;

            return UIStrings::GetStr(STR_QUICK_SET)
                    + ": "
                    + QString::number(totalValue)
                    + UIStrings::GetStr(STR_UNIT_LPERMIN) + " 100" + UIStrings::GetStr(STR_UNIT_PERCENTAGE) + "O2";
        }
            break;
        default:
            return "";
        }
        return "";
    });
    InitValue();
}

void FlowmeterManager::RegisterFlometerWidget(FlowmeterView *widget)
{
    if (!widget)
    {
        DebugAssert(0);
    }
    mFlowmeterWidget=widget;

    static std::once_flag flag;
    std::call_once(flag, [](){
        SystemTimeManager::GetInstance()->RegisterTimer(1000, FlowmeterManager::RefurbishValue);
    });
}

void FlowmeterManager::RegisterFlowControlButton(int type, SettingSpinbox *button)
{
    mAllControlButton[type] = button;

    button->setZoomScale(FLOW_CONTROL_ZOOM);
    button->setDigit(FLOW_CONTROL_DIGIT);
    switch(type)
    {
        case CONTROL_BUTTON_TYPE::TOTAL:
        {
            button->setName(STR_TOTAL);
            button->setUnit(STR_UNIT_LPERMIN);
            button->setBaseMinMax(OFF, MAX_DIRECT_FLOW_VELOCITY * 2 * 10);
            button->SetPopupInputRegex(FLOW_INPUT_REGEX);
        }
            break;
        case CONTROL_BUTTON_TYPE::O2:
        {
            button->setName((STR_GASNAME_O2));
            button->setUnit((STR_UNIT_LPERMIN));
            auto o2FlowData = new O2Data;
            o2FlowData->SetZero(false);
            button->setDataModel(o2FlowData);
            button->setBaseMinMax(OFF, MAX_DIRECT_FLOW_VELOCITY * 10);
            button->SetPopupInputRegex(FLOW_INPUT_REGEX);
        }
            break;
        case CONTROL_BUTTON_TYPE::O2PCT:
        {
            button->setZoomScale(1);
            button->setDigit(0);
            button->setName((STR_GASNAME_O2));
            button->setUnit((STR_UNIT_PERCENTAGE));
            button->setBaseMinMax(MIN_O2_PCT_WITH_AIR, PCT_100);
            button->SetPopupInputRegex(PCT_INPUT_REGEX);
        }
            break;
        case CONTROL_BUTTON_TYPE::N2O:
        {
            button->setName((STR_GASNAME_N2O));
            button->setUnit((STR_UNIT_LPERMIN));
            button->setBaseMinMax(OFF, MAX_DIRECT_FLOW_VELOCITY * 10);
            button->SetPopupInputRegex(FLOW_INPUT_REGEX);

        }
            break;
        case CONTROL_BUTTON_TYPE::AIR:
        {
            button->setName((STR_GASNAME_AIR));
            button->setUnit((STR_UNIT_LPERMIN));
            button->setBaseMinMax(OFF, MAX_DIRECT_FLOW_VELOCITY * 10);
            button->SetPopupInputRegex(FLOW_INPUT_REGEX);
        }
            break;
    }
    button->refreshText();

    connect(button, &SettingSpinbox::SignalValueChanged, this, &FlowmeterManager::slotFlowControlValueChange);
    connect(button, &SettingSpinbox::SignalEditState, this, &FlowmeterManager::SlotStartEdit);
    connect(button, (void(SettingSpinbox::*)(int))&SettingSpinbox::SignalValueChanging, this, &FlowmeterManager::SlotValueChanging);
}

void FlowmeterManager::RegisterFlowControlButton(int type, FlowControlButton *button)
{
    mAllFlowControlButton[type] = button;

    switch(type)
    {
        case CONTROL_BUTTON_TYPE::TOTAL:
        {
            button->SetTypeId(CONTROL_BUTTON_TYPE::TOTAL);
            button->SetFlowName(UIStrings::GetStr(STR_TOTAL));
            button->SetFlowUnit(UIStrings::GetStr(STR_UNIT_LPERMIN));
            button->SetPopupInputRegex(FLOW_INPUT_REGEX);
        }
            break;
        case CONTROL_BUTTON_TYPE::O2:
        {
            auto o2FlowData = new O2Data;
            o2FlowData->SetZero(false);
            button->setDataModel(o2FlowData);
            button->SetTypeId(CONTROL_BUTTON_TYPE::O2);
            button->SetFlowName(UIStrings::GetStr(STR_GASNAME_O2));
            button->SetFlowUnit(UIStrings::GetStr(STR_UNIT_LPERMIN));
            button->SetPopupInputRegex(FLOW_INPUT_REGEX);
        }
            break;
        case CONTROL_BUTTON_TYPE::O2PCT:
        {
            button->SetTypeId(CONTROL_BUTTON_TYPE::O2PCT);
            button->SetFlowName(UIStrings::GetStr(STR_GASNAME_O2));
            button->SetFlowUnit(UIStrings::GetStr(STR_UNIT_PERCENTAGE));
            button->SetPopupInputRegex(PCT_INPUT_REGEX);
        }
            break;
        case CONTROL_BUTTON_TYPE::N2O:
        {
            button->SetTypeId(CONTROL_BUTTON_TYPE::N2O);
            button->SetFlowName(UIStrings::GetStr(STR_GASNAME_N2O));
            button->SetFlowUnit(UIStrings::GetStr(STR_UNIT_LPERMIN));
            button->SetPopupInputRegex(FLOW_INPUT_REGEX);
        }
            break;
        case CONTROL_BUTTON_TYPE::AIR:
        {
            button->SetTypeId(CONTROL_BUTTON_TYPE::AIR);
            button->SetFlowName(UIStrings::GetStr(STR_GASNAME_AIR).remove(":"));
            button->SetFlowUnit(UIStrings::GetStr(STR_UNIT_LPERMIN));
            button->SetPopupInputRegex(FLOW_INPUT_REGEX);
        }
            break;
    }

    connect(button, &FlowControlButton::SignalBeginEdit, this, &FlowmeterManager::SlotFlowButtonStartEdit);
    connect(button, &FlowControlButton::SignalValueChanged, this, &FlowmeterManager::slotFlowControlValueChange);
    if(mAllControlButton.size() == mAllFlowControlButton.size()
            && mAllFlowControlButton.size() == CONTROL_BUTTON_TYPE::MAX_BUTTON_CNT) //所有控件全部注册
    {
        RefurbishAllFlowControl();
    }

}

FlowControlValueSt FlowmeterManager::ProcessValueAfterControlTypeChangeToTotal(double totalValue, double o2Pct)
{
    int controlType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    int balanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);

    o2Pct = QString::number(o2Pct + FLOAT_CAL_DEVIATION_FACTOR, 'f', 1).toDouble();
    double tmpBalancePct = 100 - o2Pct;

    while (true)
    {
        int flag = 0;

        switch(DataValidator(CONTROL_BUTTON_TYPE::O2, o2Pct, controlType, balanceType, totalValue, INVALID_VALUE, tmpBalancePct))
        {
        case DATA_VALIDATOR_ERROR_CODE::BALANCE_GAS_OUT_OF_HIGH_LIMIT:
            totalValue -= MIN_VELOCITY_PRECISION;
            break;
        case DATA_VALIDATOR_ERROR_CODE::BALANCE_GAS_OUT_OF_LOW_LIMIT:
            totalValue += MIN_VELOCITY_PRECISION;
            break;
        case DATA_VALIDATOR_ERROR_CODE::O2_VELOCITY_OUT_OF_HIGH_LIMIT:
            totalValue -= MIN_VELOCITY_PRECISION;
            break;
        case DATA_VALIDATOR_ERROR_CODE::O2_VELOCITY_OUT_OF_LOW_LIMIT:
        case DATA_VALIDATOR_ERROR_CODE::O2_PCT_TOO_LOW:
            o2Pct = o2Pct + MIN_PCT_PRECISION;
            tmpBalancePct = 100 - o2Pct;
            break;

        case DATA_VALIDATOR_ERROR_CODE::TOTAL_VELOCITY_OUT_OF_HIGH_LIMIT:
        case DATA_VALIDATOR_ERROR_CODE::TOTAL_VELOCITY_OUT_OF_LOW_LIMIT:
            break;

        case DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR:
            flag = 1;
            break;
        }
        if (flag)
            break;
    }

    return {totalValue, o2Pct, 100 - o2Pct};
}

void FlowmeterManager::ChangeFlowControlType(int type, bool IsRecord)
{
    int oldType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    int balanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
    double totalValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE);
    double o2Value = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);
    double balanceValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE);

    if (type != oldType)
    {
        SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE, type);

        if (type == CONTROL_TYPE::TOTAL_CONTROL)
        {
            if (balanceType != BALANCE_GAS_TYPE::NONE)
            {
                totalValue = o2Value + balanceValue;

                const int minO2Pct = balanceType == BALANCE_GAS_TYPE::AIR_BALANCE ? MIN_O2_PCT_WITH_AIR : MIN_O2_PCT_WITH_N2O;
                double o2Pct = minO2Pct;

                if (totalValue != OFF && balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
                {
                    o2Pct = Formula3(totalValue, o2Value);
                }
                else if (balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
                {
                    o2Pct = Formula5(o2Value, balanceValue, totalValue);
                }

                if (o2Pct > minO2Pct)
                {
                    FlowControlValueSt st = ProcessValueAfterControlTypeChangeToTotal(totalValue, o2Pct);
                    totalValue = st.totalValue;
                    o2Pct = st.o2Value;
                }
                else
                {
                    o2Pct = minO2Pct;
                }

                SaveSettingData(totalValue, o2Pct, 100 - o2Pct);
                mO2UserInputValue = o2Pct;
            }
            else
            {
                SaveSettingData(o2Value, PCT_100, OFF);
                mO2UserInputValue = PCT_100;
            }
        }
        else if (type == CONTROL_TYPE::DIRECT_FLOW)
        {
            //单管模式保留一位小数
            auto ConvertCheckAndProcess = [this](double& totalValue, double& o2Value, double& balanceValue)
            {
                o2Value = QString::number(o2Value, 'f', 1).toDouble();
                balanceValue = QString::number(balanceValue, 'f', 1).toDouble();
                totalValue = QString::number(o2Value + balanceValue, 'f', 1).toDouble();

                if (!DataValidator(CONTROL_BUTTON_TYPE::TOTAL, totalValue))
                {
                    DebugLog << "convert error" << __FUNCTION__;
                }
            };


            if (balanceType != BALANCE_GAS_TYPE::NONE)
            {
                int minO2Pct = balanceType == BALANCE_GAS_TYPE::AIR_BALANCE ? MIN_O2_PCT_WITH_AIR : MIN_O2_PCT_WITH_N2O;
                balanceValue = 100.0 - o2Value;

                if(o2Value < minO2Pct && balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
                {
                    balanceValue = Formula1(totalValue, balanceValue);
                    o2Value = Formula4(balanceValue, minO2Pct);

                    ConvertCheckAndProcess(totalValue, o2Value, balanceValue);
                }
                //氧氣的百分比表示的是濃度 從total control轉換到direct control 要進行轉換
                else if (balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
                {
                    balanceValue = Formula2(totalValue, balanceValue);
                    o2Value = totalValue - balanceValue;

                    ConvertCheckAndProcess(totalValue, o2Value, balanceValue);
                }
                else
                {
                    balanceValue = Formula1(totalValue, balanceValue);
                    o2Value = totalValue - balanceValue;

                    ConvertCheckAndProcess(totalValue, o2Value, balanceValue);
                }
                SaveSettingData(totalValue, o2Value, balanceValue);
            }
            else
            {
                totalValue = QString::number(totalValue, 'f', 1).toDouble();
                SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE, totalValue);
            }
        }
        else
        {
            DebugLog << "no this control type" << type;
        }
        RefurbishAllFlowControl();
        if(RunModeManage::GetInstance()->IsModeActive(MODE_RUN_VENT))
        {
           UpValueToFlowModel();
        }

        if(IsRecord)
        {
            HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
                                                                OPERATE_EVENT_TYPE::LOG_FLOWMETER_CHANGE,
                                                                CONTROL_MODE_CHANGE,
                                                                oldType,
                                                                type);
        }

    }
}


bool FlowmeterManager::ChangeFlowControlValue(int inputType, double newValue, bool IsRecord)
{
    int controlType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);

    double totalValue   = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE);
    double o2Value      = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);
    double balanceGasValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE);

    double oldValue{};
    switch(inputType)
    {
    case CONTROL_BUTTON_TYPE::AIR:
    case CONTROL_BUTTON_TYPE::N2O:
        oldValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE);
        break;
    case CONTROL_BUTTON_TYPE::O2:
    case CONTROL_BUTTON_TYPE::O2PCT:
        oldValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);
        break;
    case CONTROL_BUTTON_TYPE::TOTAL:
        oldValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE);
        break;
    }


    FlowControlValueSt st = {totalValue, o2Value, balanceGasValue};
    switch(inputType)
    {
    case CONTROL_BUTTON_TYPE::AIR:
    case CONTROL_BUTTON_TYPE::N2O:
        st.balanceGasValue = newValue;
        break;
    case CONTROL_BUTTON_TYPE::O2:
    case CONTROL_BUTTON_TYPE::O2PCT:
        st.o2Value = newValue;
        break;
    case CONTROL_BUTTON_TYPE::TOTAL:
        st.totalValue = newValue;
        break;
    }
    SaveSettingData(st.totalValue, st.o2Value, st.balanceGasValue);


    double value{newValue};
    int digit{};
    if (inputType == CONTROL_BUTTON_TYPE::TOTAL)
    {
        digit = 1;
    }
    else
    {
        digit = controlType == CONTROL_TYPE::DIRECT_FLOW ? 1 : 0;
    }
    if(oldValue != value && IsRecord)
    {
        oldValue = oldValue * qPow(10, digit);
        value = value * qPow(10, digit);
        HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
                                                            OPERATE_EVENT_TYPE::LOG_FLOWMETER_CHANGE,
                                                            VALUE_CHANGE,
                                                            inputType,
                                                            oldValue,
                                                            value,
                                                            controlType);
    }


    RefurbishAllFlowControl();
    if(RunModeManage::GetInstance()->IsModeActive(MODE_RUN_VENT))
    {
       UpValueToFlowModel();
    }


    return true;
}

void FlowmeterManager::ChangeFlowBalanceType(int newBalanceType, bool IsRecord)
{
    int oldBalanceType  = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
    int controlType     = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    double totalValue   = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE);

    if(newBalanceType != oldBalanceType)
    {
        SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE, newBalanceType);

        if(controlType == CONTROL_TYPE::TOTAL_CONTROL)
        {
            if (newBalanceType != BALANCE_GAS_TYPE::NONE)
            {
                FlowControlValueSt st = ProcessBalanceTypeChangeInTotalControl(oldBalanceType, newBalanceType);
                SaveSettingData(st.totalValue, st.o2Value, st.balanceGasValue);
                mO2UserInputValue = st.o2Value;
            }
            else if(newBalanceType == BALANCE_GAS_TYPE::NONE)
            {
                if(totalValue == 0)
                {
                    SaveSettingData(OFF, PCT_100, OFF);
                    mO2UserInputValue = PCT_100;
                }
                else
                {
                    SaveSettingData(totalValue > MAX_DIRECT_FLOW_VELOCITY ? MAX_DIRECT_FLOW_VELOCITY :
                                                                            (totalValue < MIN_O2_VELOCITY_LPM ? MIN_O2_VELOCITY_LPM : totalValue), PCT_100, OFF);
                    mO2UserInputValue = PCT_100;
                }

            }
        }
        else if(controlType == CONTROL_TYPE::DIRECT_FLOW)
        {
            FlowControlValueSt st = ProcessBalanceTypeChangeInDirectControl(oldBalanceType, newBalanceType);
            SaveSettingData(st.totalValue, st.o2Value, st.balanceGasValue);
        }

        if(IsRecord)
        {
            HistoryEventManager::GetInstance()->AddHistoryEvent(HistoryEventManager::HISTORY_EVENT_TYPE::OPERATE_EVENT,
                                                                OPERATE_EVENT_TYPE::LOG_FLOWMETER_CHANGE,
                                                                BALANCE_MODE_CHANGE,
                                                                oldBalanceType,
                                                                newBalanceType);
        }


        if(RunModeManage::GetInstance()->IsModeActive(MODE_RUN_VENT))
        {
           UpValueToFlowModel();
        }
        else if(RunModeManage::GetInstance()->GetCurRunMode() == MODE_RUN_STANDBY)
        {
           StopFlow();
        }

        RefurbishAllFlowControl();
    }
}

void FlowmeterManager::RefurbishAllFlowControl()
{
    int controlType   = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    int balanceType   = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
    double totalValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE)*qPow(10.0,FLOW_CONTROL_DIGIT);
    double o2Value    = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);
    double Value      = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE);

    for(int i = 0; i < mAllControlButton.size(); i ++)
    {
        mAllControlButton[i]->setVisible(false);
    }
    for(int i = 0; i < mAllFlowControlButton.size(); i ++)
    {
        mAllFlowControlButton[i]->setVisible(false);
    }


    if(controlType == CONTROL_TYPE::TOTAL_CONTROL)
    {
        totalValue = QString::number(totalValue, 'f', 0).toDouble();
        o2Value = QString::number(o2Value, 'f', 0).toInt();//百分比在内部以浮点数保存
        mO2UserInputValue = QString::number(mO2UserInputValue, 'f', 0).toInt();

        DebugLog << "[O2 Value Debug] User Input:" << mO2UserInputValue
                 << "%, Actual Control:" << o2Value
                 << "%";

        if(balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
        {
             mAllControlButton[CONTROL_BUTTON_TYPE::O2PCT]->setBaseMinMax(MIN_O2_PCT_WITH_AIR, PCT_100);
             mAllControlButton[CONTROL_BUTTON_TYPE::TOTAL]->setDataModel(new commonInnerData);
             mAllFlowControlButton[CONTROL_BUTTON_TYPE::TOTAL]->setDataModel(new commonInnerData);
        }
        else
        {
            if(balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
                mAllControlButton[CONTROL_BUTTON_TYPE::O2PCT]->setBaseMinMax(MIN_O2_PCT_WITH_N2O, PCT_100);
            mAllControlButton[CONTROL_BUTTON_TYPE::TOTAL]->setDataModel(new O2Data);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::TOTAL]->setDataModel(new O2Data);
        }

        AdjustRange(CONTROL_BUTTON_TYPE::O2PCT);
        AdjustRange(CONTROL_BUTTON_TYPE::TOTAL);

        mAllControlButton[CONTROL_BUTTON_TYPE::O2PCT]->setVisible(true);
        mAllControlButton[CONTROL_BUTTON_TYPE::O2PCT]->setValue(mO2UserInputValue,false);
        mAllFlowControlButton[CONTROL_BUTTON_TYPE::O2PCT]->setVisible(true);
        mAllFlowControlButton[CONTROL_BUTTON_TYPE::O2PCT]->SetFlowValue(mO2UserInputValue,0);

        mAllControlButton[CONTROL_BUTTON_TYPE::TOTAL]->setVisible(true);
        mAllControlButton[CONTROL_BUTTON_TYPE::TOTAL]->setValue(totalValue,false);
        mAllFlowControlButton[CONTROL_BUTTON_TYPE::TOTAL]->setVisible(true);
        mAllFlowControlButton[CONTROL_BUTTON_TYPE::TOTAL]->SetFlowValue(totalValue,1);
    }
    else if(controlType == CONTROL_TYPE::DIRECT_FLOW)
    {
        o2Value  =  QString::number(o2Value, 'f', 1).toDouble();
        Value    =  QString::number(Value, 'f', 1).toDouble();
        o2Value  *= qPow(10.0, FLOW_CONTROL_DIGIT);
        Value    *= qPow(10.0, FLOW_CONTROL_DIGIT);

        AdjustRange(CONTROL_BUTTON_TYPE::O2);
        if(balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
        {
            AdjustRange(CONTROL_BUTTON_TYPE::AIR);

            mAllControlButton[CONTROL_BUTTON_TYPE::O2]->setVisible(true);
            mAllControlButton[CONTROL_BUTTON_TYPE::O2]->setValue(o2Value, false);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::O2]->setVisible(true);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::O2]->SetFlowValue(o2Value,1);

            mAllControlButton[CONTROL_BUTTON_TYPE::AIR]->setVisible(true);
            mAllControlButton[CONTROL_BUTTON_TYPE::AIR]->setValue(Value,false);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::AIR]->setVisible(true);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::AIR]->SetFlowValue(Value,1);
        }
        else if(balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
        {
            AdjustRange(CONTROL_BUTTON_TYPE::N2O);

            mAllControlButton[CONTROL_BUTTON_TYPE::O2]->setVisible(true);
            mAllControlButton[CONTROL_BUTTON_TYPE::O2]->setValue(o2Value,false);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::O2]->setVisible(true);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::O2]->SetFlowValue(o2Value,1);

            mAllControlButton[CONTROL_BUTTON_TYPE::N2O]->setVisible(true);
            mAllControlButton[CONTROL_BUTTON_TYPE::N2O]->setValue(Value,false);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::N2O]->setVisible(true);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::N2O]->SetFlowValue(Value,1);
        }
        else //NONE
        {
            mAllControlButton[CONTROL_BUTTON_TYPE::O2]->setVisible(true);
            mAllControlButton[CONTROL_BUTTON_TYPE::O2]->setValue(o2Value,false);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::O2]->setVisible(true);
            mAllFlowControlButton[CONTROL_BUTTON_TYPE::O2]->SetFlowValue(o2Value,1);
        }
    }
}

/* *
 * @brief FlowmeterManager::UpValueToFlowModel 發送全電子流量計設定至下位機
   */
void FlowmeterManager::UpValueToFlowModel()
{
    int controlType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    int balanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
    double totalValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE)*qPow(10.0,FLOW_CONTROL_DIGIT);
    double o2Value = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);
    double balanceValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE);

    //氧氣的百分比表示的是濃度（纯氧气 + 空气中氧气） 要進行轉換
    if (controlType == CONTROL_TYPE::TOTAL_CONTROL && balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
    {
        balanceValue = totalValue * (PCT_100 - o2Value) / 79;
        balanceValue = qRound(balanceValue);
        o2Value = totalValue - balanceValue;
    }
    else if (controlType == CONTROL_TYPE::TOTAL_CONTROL && balanceType != BALANCE_GAS_TYPE::AIR_BALANCE)
    {
        o2Value = totalValue * o2Value / 100;
        o2Value = qRound(o2Value);
        balanceValue = totalValue - o2Value;
    }

    //单位为L/min时  下位机的处理精度为小数点后两位
    //转换为整数 乘以100 不进行四舍五入
    if(controlType == CONTROL_TYPE::DIRECT_FLOW)
    {
        o2Value = qRound(o2Value*10);
        balanceValue = qRound(balanceValue*10);
    }

    //short shortTotalValue   = FlotToIntBpyDigit(totalValue,   2);
    short shortBalanceValue = FlotToIntBpyDigit(balanceValue, 1);
    short shortO2Value      = FlotToIntBpyDigit(o2Value, 1);;

    DebugLog << __FUNCTION__ << shortO2Value << balanceType << shortBalanceValue;
    Flowmeter::GetInstance()->FlowmeterValueSet(shortO2Value, balanceType, shortBalanceValue);
}


/* *
 * @brief FlowmeterManager::StopFlow  使流量计气体不流通
   */
void FlowmeterManager::StopFlow()
{
    int balanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
    Flowmeter::GetInstance()->FlowmeterValueSet(OFF, balanceType, OFF);
}

FlowmeterManager *FlowmeterManager::GetInstance()
{
    static FlowmeterManager s_flowmeter_manager;
    return &s_flowmeter_manager;
}

void FlowmeterManager::RefurbishValue(void *, void *)
{
    FlowmeterManager::GetInstance()->RefurbishAllFlowmeter();
}

short FlowmeterManager::RefurbishAllFlowmeter()
{
    mTotalValue = 0;
    for (int i = E_FLOWMETER_TYPE::FLOWMETER_O2; i < E_FLOWMETER_TYPE::FLOWMETER_MAX; i ++)
    {
        short value = 0;

        DataManager::GetInstance()->GetMonitorData(VALUEDATA_FLOWMETER_O2 + i, value);
        mMonitorValues[(E_FLOWMETER_TYPE)i] = value;
        mFlowmeterWidget->RefurbishValue((E_FLOWMETER_TYPE)i, value);

        if (INVALID_VALUE != value)
        {
            mTotalValue += value;
        }
    }

    FlowAlarmCheck();
    return 0;
}

int FlowmeterManager::FlowAlarmCheck()
{
    short o2FlowVelocity = mMonitorValues[E_FLOWMETER_TYPE::FLOWMETER_O2],
            n2oFlowVelocity = mMonitorValues[E_FLOWMETER_TYPE::FLOWMETER_N2O],
            airFlowVelocity = mMonitorValues[E_FLOWMETER_TYPE::FLOWMETER_AIR];
    if(!ConfigManager->IsFullElecFlomter())
    {
        //氧流速过大报警
        if (o2FlowVelocity > 1000)
        {
            AlarmManager::GetInstance()->TriggerAlarm(ALARM_O2_FLOW_HIGH, 1);
        }
        else
        {
            AlarmManager::GetInstance()->TriggerAlarm(ALARM_O2_FLOW_HIGH, 0);
        }

        //笑气流速过大
        if (n2oFlowVelocity > 1000)
        {
            AlarmManager::GetInstance()->TriggerAlarm(ALARM_N2O_FLOW_HIGH, 1);
        }
        else
        {
            AlarmManager::GetInstance()->TriggerAlarm(ALARM_N2O_FLOW_HIGH, 0);
        }

        //空气流速过大
        if (airFlowVelocity > 1000)
        {
            AlarmManager::GetInstance()->TriggerAlarm(ALARM_AIR_FLOW_HIGH, 1);
        }
        else
        {
            AlarmManager::GetInstance()->TriggerAlarm(ALARM_AIR_FLOW_HIGH, 0);
        }

        if(mTotalValue > 1000)
            AlarmManager::GetInstance()->TriggerAlarm(ALARM_FLOW_HIGH, 1);
        else
            AlarmManager::GetInstance()->TriggerAlarm(ALARM_FLOW_HIGH, 0);
    }

    //氧笑比例异常
    {
        //滤波
        if((!ConfigManager->IsFullElecFlomter() && ConfigManager->GetConfig<bool>(SystemConfigManager::GAS_SOURCE_N2O_BOOL_INDEX))
                ||(ConfigManager->IsFullElecFlomter()&&SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE)==BALANCE_GAS_TYPE::N2O_BALANCE))
        {
            constexpr int queueLen = 10;
            static std::deque<short> sBoolQueue(queueLen);
            if (sBoolQueue.size() == queueLen)
            {
                sBoolQueue.pop_front();
            }
            sBoolQueue.push_back(o2FlowVelocity / double(n2oFlowVelocity + o2FlowVelocity) < MIN_O2_PCT_WITH_N2O / 100.0 );

            if(std::count(sBoolQueue.cbegin(), sBoolQueue.cend(), true) == queueLen && o2FlowVelocity != INVALID_VALUE && n2oFlowVelocity != INVALID_VALUE)
            {
                AlarmManager::GetInstance()->TriggerAlarm(ALARM_O2_N2O_UNUSUAL_RATIO, 1);
            }
            else if(std::count(sBoolQueue.cbegin(), sBoolQueue.cend(), false) == queueLen || o2FlowVelocity == INVALID_VALUE || n2oFlowVelocity == INVALID_VALUE)
            {
                AlarmManager::GetInstance()->TriggerAlarm(ALARM_O2_N2O_UNUSUAL_RATIO, 0);
            }
        }
    }
    return 1;
}

void FlowmeterManager::AdjustRange(unsigned int paramType)
{
    int controlType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    int balanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
    double totalValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE);
    double o2Value    = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);
    double banlanceValue = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE);
    int inputType = paramType;
    int paramMax = mAllControlButton[paramType]->GetTrueMax();
    int paramMin = mAllControlButton[paramType]->GetTrueMin();
    int max = paramMax, min = paramMin;
    double tempMin=0.0,tempMax=0.0;
    UPDATE_RULE maxRule = NONE_RULE;
    UPDATE_RULE minRule = NONE_RULE;

    if(controlType == CONTROL_TYPE::TOTAL_CONTROL)
    {
        if(balanceType == BALANCE_GAS_TYPE::NONE)
        {
            if(inputType == CONTROL_BUTTON_TYPE::O2PCT)
            {
                min = PCT_100;
                minRule = RULE_1;
            }
            else //total
            {
                max = MAX_DIRECT_FLOW_VELOCITY * 10;
                maxRule = RULE_2;
            }
        }
        else if(balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
        {
            if(inputType == CONTROL_BUTTON_TYPE::O2PCT)
            {
                if(totalValue == 0.1) //T=0.1
                {
                    max = MIN_O2_PCT_WITH_AIR;
                    maxRule = RULE_4;
                }
                else if(totalValue > MAX_DIRECT_FLOW_VELOCITY && o2Value != MIN_O2_PCT_WITH_AIR)
                {
                    tempMin = (15.8 / totalValue + 21);                     //确保纯氧气流量 ≥ 0.2 L/min
                    double temp = 100 - 948 / totalValue;                   //确保空气流量 ≤ 12 L/min
                    min = std::min((int)ceil(temp), (int)ceil(tempMin));
                    minRule = RULE_4;

                    tempMax = (948 / totalValue + 21);                      //确保纯氧气流量 ≤ 12 L/min
                    temp = (100 - 7.9/totalValue);                          //确保空气流量 ≥ 0.1 L/min
                    max = std::max(tempMax, temp);
                    maxRule = RULE_2;
                }
                else
                {
                    min = MIN_O2_PCT_WITH_AIR;
                    max = PCT_100;
                }
            }
            else //total
            {
                if(o2Value!=MIN_O2_PCT_WITH_AIR && o2Value!=PCT_100)
                {
//                    tempMin =  15.8 / (o2Value-MIN_O2_PCT_WITH_AIR) * 10;
//                    double temp = 7.9 / (PCT_100-o2Value) * 10;
//                    min = std::min(ceil(tempMin), ceil(temp));
//                    minRule = RULE_4;
                    min = OFF;

                    tempMax = 948 / (o2Value-MIN_O2_PCT_WITH_AIR) * 10;     //纯氧气流量上限约束
                    double temp = 948 / (PCT_100-o2Value) * 10;             //空气流量上限约束
                    max = std::max(tempMax, temp);
                    maxRule = RULE_2;
                }
                else
                {
                    min = OFF;
                    max = MAX_DIRECT_FLOW_VELOCITY * 10;
                    maxRule = RULE_2;
                }
            }
        }
        else if(balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
        {
            if(inputType == CONTROL_BUTTON_TYPE::O2PCT)
            {
                if(totalValue != 0)
                {
                    tempMin = MIN_O2_VELOCITY_LPM / totalValue * 100;       //确保流量≥0.2L/min的百分比要求
                    min = std::min(MIN_O2_PCT_WITH_N2O, (int)ceil(tempMin));
                    if(min != MIN_O2_PCT_WITH_N2O)
                        minRule = RULE_4;
                    else
                        minRule = RULE_3;

                    tempMax = MAX_DIRECT_FLOW_VELOCITY / totalValue * 100;  //约束条件纯氧气流量 ≤ 12 L/min
                    max = tempMax;
                    maxRule = RULE_2;
                }
                else
                {
                    min = MIN_O2_PCT_WITH_N2O;
                    minRule = RULE_3;
                }
            }
            else
            {
               tempMin = MIN_O2_VELOCITY_LPM*100 / (o2Value) * 10;  //约束条件氧气流量 ≥ 0.2 L/min
               min = ceil(tempMin);
               minRule = RULE_4;

               if(o2Value != PCT_100)
               {
                   tempMax = MAX_DIRECT_FLOW_VELOCITY*100 / (o2Value) * 10;                 //氧气流量上限约束
                   double temp = MAX_DIRECT_FLOW_VELOCITY*100 / ((PCT_100-o2Value)) * 10;   //笑气流量上限约束
                   max = std::max(tempMax, temp);
                   maxRule = RULE_2;
               }
               else
               {
                   min = OFF;
                   max = MAX_DIRECT_FLOW_VELOCITY * 10;
                   maxRule = RULE_2;
               }
            }
        }
    }
    else if(controlType == CONTROL_TYPE::DIRECT_FLOW)
    {
        if(balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
        {
            if(inputType == CONTROL_BUTTON_TYPE::N2O)
            {
                tempMax = o2Value * 3.0 * 10;           //约束O2% ≥ 25%
                max = tempMax;
                maxRule = RULE_3;
            }
            else //O2
            {
                tempMin = ceil(banlanceValue * 10.0 / 3);   //约束 N2O流量 ≤ 3 × O2流量，反推
                min = tempMin;
                minRule = RULE_3;
                if(min == OFF) //能输0,仅不能输0.1
                    minRule = RULE_4;
            }
        }
    }
    if(max >= paramMax)
    {
        maxRule = NONE_RULE;
    }
    if(min <= paramMin && minRule != RULE_4)
    {
        minRule = NONE_RULE;
    }
    paramMax = std::min(max, paramMax);
    paramMin = std::max(min, paramMin);
    UpOutMaxTip(paramType, maxRule);
    UpOutMinTip(paramType, minRule);
    mAllControlButton[paramType]->SetRange(paramMin, paramMax, true);
    mAllFlowControlButton[paramType]->SetRange(paramMin, paramMax, true);
}

void FlowmeterManager::UpOutMaxTip(unsigned int paramType, int rule)
{
    QString str = UIStrings::GetStr(STR_OUT_OF_UPPER_LIMIT);
    switch ((UPDATE_RULE)rule)
    {
    case RULE_1:
    {
    }
    break;
    case RULE_2:
    {
        str += "(" + UIStrings::GetStr(STR_DIRECT_FLOW) + " " + UIStrings::GetStr(STR_GRAPHNAME_FLOW) + ">12" + UIStrings::GetStr(STR_UNIT_LPERMIN) + ")";
    }
        break;
    case RULE_3:
    {
        str = UIStrings::GetStr(STR_FLOWMETER_O2_CONCENTRATION_TIPS);
    }
        break;
    case RULE_4:
    {
        str = UIStrings::GetStr(STR_FLOWMETER_O2_FLOW_TIPS);
    }
        break;
    default:
        break;
    }
    mAllControlButton[paramType]->SetErrorValueTipStr(IntraData::OUT_MAX,str);
    if(mAllFlowControlButton.contains(paramType))
        mAllFlowControlButton[paramType]->SetErrorValueTipStr(IntraData::OUT_MAX,str);
}

void FlowmeterManager::UpOutMinTip(unsigned int paramType, int rule)
{
    QString str = UIStrings::GetStr(STR_OUT_OF_LOWER_LIMIT);
    switch ((UPDATE_RULE)rule)
    {
    case RULE_1:
    {
        str += "(O2<100%)";
    }
    break;
    case RULE_2:
    {
    }
        break;
    case RULE_3:
    {
        str = UIStrings::GetStr(STR_FLOWMETER_O2_CONCENTRATION_TIPS);
    }
        break;
    case RULE_4:
    {
        str = UIStrings::GetStr(STR_FLOWMETER_O2_FLOW_TIPS);
    }
        break;
    default:
        break;
    }
    mAllControlButton[paramType]->SetErrorValueTipStr(IntraData::OUT_MIN,str);
    if(mAllFlowControlButton.contains(paramType))
        mAllFlowControlButton[paramType]->SetErrorValueTipStr(IntraData::OUT_MIN,str);
}

void FlowmeterManager::slotFlowControlValueChange(int value)
{
    int valueType = 0;
    int controlType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    auto button = qobject_cast<SettingSpinbox *>(sender());
    if(button)
        valueType = mAllControlButton.key(button, -1);
    else
        valueType = mAllFlowControlButton.key(qobject_cast<FlowControlButton *>(sender()), -1);

    if(controlType == CONTROL_TYPE::TOTAL_CONTROL)
    {
        if(valueType == CONTROL_BUTTON_TYPE::O2PCT)
        {
            mO2UserInputValue = value;
        }
        CalValidValue(valueType, value);
    }

    double trueValue = value;
    if(valueType >= CONTROL_BUTTON_TYPE::AIR)
    {
        if(valueType != CONTROL_BUTTON_TYPE::TOTAL)
        {
            int digit = 0;
            if(controlType == CONTROL_TYPE::DIRECT_FLOW)
            {
               digit = FLOW_CONTROL_DIGIT;
            }
            trueValue /= qPow(10.0, digit);
        }
        if(valueType == CONTROL_BUTTON_TYPE::TOTAL)
            trueValue /= qPow(10.0, FLOW_CONTROL_DIGIT);

        ChangeFlowControlValue(valueType, trueValue);
    }
}

void FlowmeterManager::SlotStartEdit(bool state)
{
    if(state)
    {
        SettingSpinbox *sendSpinbox = qobject_cast<SettingSpinbox *>(sender());
        AdjustRange(mAllControlButton.key(sendSpinbox));
        SlotValueChanging(sendSpinbox->value());
    }
}

void FlowmeterManager::SlotValueChanging(int)
{
    DebugAssert(senderSignalIndex());

    int controlType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    QString str = "";
    if(controlType == DIRECT_FLOW)
    {
        int total = mAllControlButton[CONTROL_BUTTON_TYPE::O2]->value();
        int balanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
        if(balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
        {
            total += mAllControlButton[CONTROL_BUTTON_TYPE::AIR]->value();
        }
        else if(balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
        {
            total += mAllControlButton[CONTROL_BUTTON_TYPE::N2O]->value();
        }
        str += UIStrings::GetStr(STR_TOTAL) + QString(":%1").arg(total / 10.0, 0, 'f', 1) + UIStrings::GetStr(STR_UNIT_LPERMIN);
    }
    SettingSpinbox *button = qobject_cast<SettingSpinbox *>(sender());
    if(button)
        button->setITimeTipStr(str);
    else
    {
        FlowControlButton *button = qobject_cast<FlowControlButton *>(sender());
        button->setITimeTipStr(str);
    }
}

void FlowmeterManager::SlotFlowButtonStartEdit()
{
    auto button = qobject_cast<FlowControlButton *>(sender());
    AdjustRange(mAllFlowControlButton.key(button));
    SlotValueChanging(button->value());
}


void FlowmeterManager::SaveSettingData(double totalValue, double o2Value, double balanceValue)
{
    int controlType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    int dataDigit   = 1; /* controlType == CONTROL_TYPE::DIRECT_FLOW ? 1 : 0  */


    o2Value      = QString::number(o2Value + FLOAT_CAL_DEVIATION_FACTOR, 'f', dataDigit).toDouble();
    balanceValue = QString::number(balanceValue + FLOAT_CAL_DEVIATION_FACTOR, 'f', dataDigit).toDouble();
    if (controlType == CONTROL_TYPE::DIRECT_FLOW)
        totalValue = QString::number(o2Value + balanceValue + FLOAT_CAL_DEVIATION_FACTOR, 'f', 1).toDouble();
    else
        totalValue = QString::number(totalValue + FLOAT_CAL_DEVIATION_FACTOR, 'f', 1).toDouble();


    SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE, totalValue);
    SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE, o2Value);
    SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE, balanceValue);
}

void FlowmeterManager::CalValidValue(int valueType, int &value)
{
    int balanceType = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
    double totalFlow = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE)*qPow(10.0,FLOW_CONTROL_DIGIT);
    double o2Per = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);
    double balanceFlow = 0.0;
    double o2Flow = 0.0;
    double tempValue = value;

    //调o2Flow或调balanceFlow或都不调
    switch (valueType)
    {
        case CONTROL_BUTTON_TYPE::O2PCT:
        {
            o2Per = value;
            if(balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
            {
                balanceFlow = totalFlow * (PCT_100 - o2Per) / 79;
                o2Flow = totalFlow - balanceFlow;
                AdjustPrecision(FLOWMETER_O2, o2Flow);

                if(totalFlow == MIN_BALANCE_FLOW_VELOCITY_LPM*10) //只能纯AIR
                {
                    tempValue = MIN_O2_PCT_WITH_AIR;
                }
                else if(totalFlow != OFF)
                {
                    balanceFlow = totalFlow - o2Flow;
                    AdjustPrecision(FLOWMETER_AIR, balanceFlow);
                    double calculatedO2Pct = PCT_100 - (balanceFlow * 79 / totalFlow);

                    if(qRound(calculatedO2Pct) < o2Per)
                    {
                        int maxIterations = 100;
                        int iterations = 0;
                        double adjustedO2Pct = o2Per;
                        while(calculatedO2Pct < o2Per && adjustedO2Pct < PCT_100 && iterations < maxIterations)
                        {
                            adjustedO2Pct += 1.0;
                            balanceFlow = totalFlow * (PCT_100 - adjustedO2Pct) / 79;
                            o2Flow = totalFlow - balanceFlow;
                            AdjustPrecision(FLOWMETER_O2, o2Flow);
                            balanceFlow = totalFlow - o2Flow;
                            AdjustPrecision(FLOWMETER_AIR, balanceFlow);
                            calculatedO2Pct = PCT_100 - (balanceFlow * 79 / totalFlow);
                            iterations++;
                        }
                    }
                    tempValue = calculatedO2Pct;
                }
            }
            else if(balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
            {
                balanceFlow = totalFlow * (PCT_100 - o2Per) / PCT_100;
                o2Flow = totalFlow - balanceFlow;
                AdjustPrecision(FLOWMETER_O2, o2Flow);

                if(o2Flow == OFF && totalFlow != o2Flow)
                    o2Flow = MIN_O2_VELOCITY_LPM*10;
                if(totalFlow != OFF)
                {
                    balanceFlow = totalFlow - o2Flow;
                    AdjustPrecision(FLOWMETER_N2O, balanceFlow);
                    double calculatedO2Pct = PCT_100 - (balanceFlow / totalFlow) * 100;

                    if(qRound(calculatedO2Pct) < o2Per)
                    {
                        int maxIterations = 100;
                        int iterations = 0;
                        double adjustedO2Pct = o2Per;
                        while(calculatedO2Pct < o2Per && adjustedO2Pct < PCT_100 && iterations < maxIterations)
                        {
                            adjustedO2Pct += 1.0;
                            balanceFlow = totalFlow * (PCT_100 - adjustedO2Pct) / PCT_100;
                            o2Flow = totalFlow - balanceFlow;
                            AdjustPrecision(FLOWMETER_O2, o2Flow);
                            balanceFlow = totalFlow - o2Flow;
                            AdjustPrecision(FLOWMETER_N2O, balanceFlow);
                            calculatedO2Pct = PCT_100 - (balanceFlow / totalFlow) * 100;
                            iterations++;
                        }
                    }
                    tempValue = calculatedO2Pct;
                }
            }
            break;
        }
        case CONTROL_BUTTON_TYPE::TOTAL:
        {
            totalFlow = value;
            if(balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
            {
                balanceFlow = totalFlow * (PCT_100 - o2Per) / 79;
                o2Flow = totalFlow - balanceFlow;
                bool o2PrecisionAdjusted = AdjustPrecision(FLOWMETER_O2, o2Flow);
                balanceFlow = totalFlow - o2Flow;
                bool balancePrecisionAdjusted = AdjustPrecision(FLOWMETER_AIR, balanceFlow);

                if(o2PrecisionAdjusted || balancePrecisionAdjusted)
                {
                    double adjustedO2Pct = o2Per;
                    int maxIterations = 100;
                    int iterations = 0;

                    while((o2PrecisionAdjusted || balancePrecisionAdjusted) && adjustedO2Pct < PCT_100 && iterations < maxIterations)
                    {
                        adjustedO2Pct += 1.0;
                        balanceFlow = totalFlow * (PCT_100 - adjustedO2Pct) / 79;
                        o2Flow = totalFlow - balanceFlow;
                        o2PrecisionAdjusted = AdjustPrecision(FLOWMETER_O2, o2Flow);
                        balanceFlow = totalFlow - o2Flow;
                        balancePrecisionAdjusted = AdjustPrecision(FLOWMETER_AIR, balanceFlow);
                        iterations++;
                    }

                    if(adjustedO2Pct != o2Per)
                    {
                        SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE, adjustedO2Pct);
                    }
                }
            }
            else if(balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
            {
                balanceFlow = totalFlow * (PCT_100 - o2Per) / PCT_100;
                o2Flow = totalFlow - balanceFlow;
                bool o2PrecisionAdjusted = AdjustPrecision(FLOWMETER_O2, o2Flow);

                if(o2Flow == OFF && totalFlow != o2Flow)
                    o2Flow = MIN_O2_VELOCITY_LPM*10;

                balanceFlow = totalFlow - o2Flow;
                bool balancePrecisionAdjusted = AdjustPrecision(FLOWMETER_N2O, balanceFlow);

                if(o2PrecisionAdjusted || balancePrecisionAdjusted)
                {
                    double adjustedO2Pct = o2Per;
                    int maxIterations = 100;
                    int iterations = 0;

                    while((o2PrecisionAdjusted || balancePrecisionAdjusted) && adjustedO2Pct < PCT_100 && iterations < maxIterations)
                    {
                        adjustedO2Pct += 1.0;
                        balanceFlow = totalFlow * (PCT_100 - adjustedO2Pct) / PCT_100;
                        o2Flow = totalFlow - balanceFlow;
                        o2PrecisionAdjusted = AdjustPrecision(FLOWMETER_O2, o2Flow);

                        if(o2Flow == OFF && totalFlow != o2Flow)
                            o2Flow = MIN_O2_VELOCITY_LPM*10;

                        balanceFlow = totalFlow - o2Flow;
                        balancePrecisionAdjusted = AdjustPrecision(FLOWMETER_N2O, balanceFlow);
                        iterations++;
                    }

                    if(adjustedO2Pct != o2Per)
                    {
                        SettingManager->SetSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE, adjustedO2Pct);
                    }
                }
            }
            break;
        }
        default:
            break;
    }
    value = qRound(tempValue);
    value = std::min(mAllControlButton[valueType]->GetMaxVal(), value);
    value = std::max(mAllControlButton[valueType]->GetMinVal(), value);
}

bool FlowmeterManager::AdjustPrecision(int valueType, double &value)
{
    double temp = value;
    switch (valueType)
    {
        case FLOWMETER_O2:
        {
            value = qRound(value);
            if(value < MIN_O2_VELOCITY_LPM *10 && fabs(value - OFF) > DOUBLE_EQUAL_DIF)
            {
               value = value <= 0.1*10 ? OFF : MIN_O2_VELOCITY_LPM*10;
            }
            if(value > MAX_DIRECT_FLOW_VELOCITY*10)
            {
               value = MAX_DIRECT_FLOW_VELOCITY*10;
            }
            break;
        }
        case FLOWMETER_AIR:
        case FLOWMETER_N2O:
        {
            if(value < MIN_BALANCE_FLOW_VELOCITY_LPM*10 && fabs(value - OFF) > DOUBLE_EQUAL_DIF)
            {
                value = value < 0.05*10 ? OFF : MIN_BALANCE_FLOW_VELOCITY_LPM*10;
            }
            if(value > MAX_DIRECT_FLOW_VELOCITY*10)
            {
                value = MAX_DIRECT_FLOW_VELOCITY*10;
            }
            break;
        }
        default:
            break;
    }
    return temp != value;
}

bool FlowmeterManager::DataValidator(int inputType, double inputValue)
{
    int     controlType     = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_TYPE);
    int     balanceType     = SettingManager->GetIntSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_TYPE);
    double  totalValue      = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_TOTAL_VALUE);
    double  balanceValue    = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_BANLANCE_GAS_VALUE);
    double  o2Value         = SettingManager->GetFloatSettingValue(SystemSettingManager::FLOW_CONTROL_O2_VALUE);

    return DataValidator(inputType, inputValue, controlType, balanceType, totalValue, o2Value, balanceValue) == DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR ? true : false;
}

/* *
 * @brief FlowmeterManager::DataValidator  數據驗證器
 * @param type  CONTROL_BUTTON_TYPE
 * @param value
 * @return 数据错误码
   */
DATA_VALIDATOR_ERROR_CODE FlowmeterManager::DataValidator(int inputType, double inputValue, int controlType, int balanceType, double totalValue, double o2Value, double balanceValue)
{
    if ((controlType == CONTROL_TYPE::DIRECT_FLOW || inputType == CONTROL_BUTTON_TYPE::TOTAL) && inputValue == OFF)
    {
        return DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR;
    }

    if (controlType == CONTROL_TYPE::TOTAL_CONTROL)
    {
        //总流量控制下 校驗當前百分比轉換爲流速後 是否符合最低最高流速限制
        auto CheckFlowVelocity = [balanceType](double balancePct, double totalValue) -> DATA_VALIDATOR_ERROR_CODE
        {
            double tmpBalanceVelocity = 0;
            double tmpO2Velocity      = 0;

            totalValue = QString::number(totalValue, 'f', 2).toDouble();

            if (balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
                tmpBalanceVelocity = Formula2(totalValue, balancePct);

            else if (balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
                tmpBalanceVelocity = Formula1(totalValue, balancePct);


            tmpBalanceVelocity = QString::number(tmpBalanceVelocity, 'f', 2).toDouble();
            tmpO2Velocity      = QString::number(totalValue - tmpBalanceVelocity, 'f', 2).toDouble();

            if (tmpO2Velocity < MIN_O2_VELOCITY_LPM)
                return DATA_VALIDATOR_ERROR_CODE::O2_VELOCITY_OUT_OF_LOW_LIMIT;

            if (tmpO2Velocity > MAX_DIRECT_FLOW_VELOCITY)
                return DATA_VALIDATOR_ERROR_CODE::O2_VELOCITY_OUT_OF_HIGH_LIMIT;

            if ( (balancePct != OFF || tmpBalanceVelocity != OFF) && tmpBalanceVelocity < MIN_BALANCE_FLOW_VELOCITY_LPM)
                return DATA_VALIDATOR_ERROR_CODE::BALANCE_GAS_OUT_OF_LOW_LIMIT;

            if (tmpBalanceVelocity > MAX_DIRECT_FLOW_VELOCITY)
                return DATA_VALIDATOR_ERROR_CODE::BALANCE_GAS_OUT_OF_HIGH_LIMIT;

            return DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR;
        };


        o2Value = QString::number(o2Value + FLOAT_CAL_DEVIATION_FACTOR, 'f', 1).toDouble();
        balanceValue = QString::number(100 - o2Value + FLOAT_CAL_DEVIATION_FACTOR, 'f', 1).toDouble();
        totalValue = QString::number(totalValue + FLOAT_CAL_DEVIATION_FACTOR, 'f', 2).toDouble();
        if (balanceType == BALANCE_GAS_TYPE::AIR_BALANCE)
        {
            if (inputType == CONTROL_BUTTON_TYPE::TOTAL)
            {
                //當氧氣濃度設爲21% 即可全通空氣 所以下限變爲100mlpm
                if (o2Value == AIR_OXYGEN_CONTENT && inputValue <= MAX_DIRECT_FLOW_VELOCITY)
                    return DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR;

                DATA_VALIDATOR_ERROR_CODE tmp = CheckFlowVelocity(balanceValue, inputValue);
                if(tmp != DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR)
                    return tmp;
            }
            else if (inputType == CONTROL_BUTTON_TYPE::O2)
            {
                //當氧氣濃度設爲21% 即可全通空氣 所以下限變爲100mlpm
                if(inputValue == AIR_OXYGEN_CONTENT && totalValue <= MAX_DIRECT_FLOW_VELOCITY)
                    return DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR;

                if (o2Value == AIR_OXYGEN_CONTENT && totalValue > MAX_DIRECT_FLOW_VELOCITY)
                    return DATA_VALIDATOR_ERROR_CODE::BALANCE_GAS_OUT_OF_HIGH_LIMIT;

                if(totalValue == OFF && inputValue >= MIN_O2_PCT_WITH_AIR)
                    return DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR;

                DATA_VALIDATOR_ERROR_CODE tmp = CheckFlowVelocity(100 - inputValue, totalValue);
                if(tmp != DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR)
                    return tmp;
            }
        }
        else if (balanceType == BALANCE_GAS_TYPE::N2O_BALANCE)
        {
            if (inputType == CONTROL_BUTTON_TYPE::TOTAL)
            {
                DATA_VALIDATOR_ERROR_CODE tmp = CheckFlowVelocity(balanceValue, inputValue);
                if(tmp != DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR)
                    return tmp;
            }
            else if (inputType == CONTROL_BUTTON_TYPE::O2)
            {
                if (totalValue == OFF && inputValue >= MIN_O2_PCT_WITH_N2O)
                    return DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR;

                DATA_VALIDATOR_ERROR_CODE tmp = CheckFlowVelocity(100 - inputValue, totalValue);
                if (tmp != DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR)
                    return tmp;
            }
        }
        else if (balanceType == BALANCE_GAS_TYPE::NONE)
        {
            if (inputType == CONTROL_BUTTON_TYPE::TOTAL)
            {
                if (inputValue < MIN_O2_VELOCITY_LPM)
                    return DATA_VALIDATOR_ERROR_CODE::O2_VELOCITY_OUT_OF_LOW_LIMIT;
                if (inputValue > MAX_DIRECT_FLOW_VELOCITY)
                    return DATA_VALIDATOR_ERROR_CODE::O2_VELOCITY_OUT_OF_HIGH_LIMIT;
            }
        }
    }
    else if (controlType == CONTROL_TYPE::DIRECT_FLOW)
    {
        //氧流速最小设置值为200mlpm
        if (inputType == CONTROL_BUTTON_TYPE::O2)
        {
            if (inputValue < MIN_O2_VELOCITY_LPM)
                return DATA_VALIDATOR_ERROR_CODE::O2_VELOCITY_OUT_OF_LOW_LIMIT;
        }
    }

    return DATA_VALIDATOR_ERROR_CODE::FLOW_CACL_NO_ERROR;
}

IntraData::VALUE_CHECK_TYPE O2Data::checkValue(qreal value)
{
    if(value == OFF && mIsZero)
        return VALID;
    if(value < mTrueMin && value > OFF)
        return OUT_MIN;
    return IntraData::checkValue(value);
}

void O2Data::SetRange(int minVal, int maxVal, bool isDumb)
{
    if(minVal > maxVal)
    {
        return;
    }
    if(mIsZero)
    {
        mTrueMin = std::max(minVal, 2);
        IntraData::SetRange(0,maxVal,isDumb);
    }
    else
        IntraData::SetRange(minVal,maxVal,isDumb);
}

void O2Data::SetZero(bool isZero)
{
    mIsZero = isZero;
}

void O2Data::SetTrueMin(double min)
{
    mTrueMin = min;
}
